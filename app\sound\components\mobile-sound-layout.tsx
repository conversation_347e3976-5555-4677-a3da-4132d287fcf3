"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { MainOperationPanel } from "./main-operation-panel";
import { SettingsPanel } from "./settings-panel";
import { HistoryPanel } from "./history-panel";
import { FunctionToggle } from "./function-toggle";
import { Settings, History, Menu } from "lucide-react";
import { cn } from "@/lib/utils";

type MobileView = "main" | "settings" | "history";

export function MobileSoundLayout() {
    const [activeView, setActiveView] = useState<MobileView>("main");
    const [isMenuOpen, setIsMenuOpen] = useState(false);

    const renderContent = () => {
        switch (activeView) {
            case "settings":
                return <SettingsPanel />;
            case "history":
                return <HistoryPanel />;
            default:
                return (
                    <div className="flex flex-col h-full">
                        <div className="p-4">
                            <FunctionToggle />
                        </div>
                        <MainOperationPanel />
                    </div>
                );
        }
    };

    return (
        <div className="flex flex-col h-full bg-white dark:bg-black">
            {/* Mobile Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-800">
                <h1 className="text-lg font-medium text-black dark:text-white">Sound Studio</h1>

                <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
                    <SheetTrigger asChild>
                        <Button variant="ghost" size="sm" className="text-gray-500 hover:text-black dark:hover:text-white">
                            <Menu className="w-5 h-5" />
                        </Button>
                    </SheetTrigger>
                    <SheetContent side="right" className="bg-white dark:bg-black border-gray-200 dark:border-gray-800 w-80">
                        <div className="py-4">
                            <h2 className="text-black dark:text-white font-medium mb-4">Menu</h2>
                            <div className="space-y-2">
                                <Button
                                    variant="ghost"
                                    className="w-full justify-start text-gray-500 hover:text-black dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-900"
                                    onClick={() => {
                                        setActiveView("main");
                                        setIsMenuOpen(false);
                                    }}
                                >
                                    Voice Generation
                                </Button>
                                <Button
                                    variant="ghost"
                                    className="w-full justify-start text-gray-500 hover:text-black dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-900"
                                    onClick={() => {
                                        setActiveView("settings");
                                        setIsMenuOpen(false);
                                    }}
                                >
                                    <Settings className="w-4 h-4 mr-2" />
                                    Settings
                                </Button>
                                <Button
                                    variant="ghost"
                                    className="w-full justify-start text-gray-500 hover:text-black dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-900"
                                    onClick={() => {
                                        setActiveView("history");
                                        setIsMenuOpen(false);
                                    }}
                                >
                                    <History className="w-4 h-4 mr-2" />
                                    History
                                </Button>
                            </div>
                        </div>
                    </SheetContent>
                </Sheet>
            </div>

            {/* Main Content */}
            <div className="flex-1 overflow-hidden">
                {renderContent()}
            </div>

            {/* Bottom Navigation */}
            <div className="flex border-t border-gray-200 dark:border-gray-800 bg-white dark:bg-black">
                <Button
                    variant="ghost"
                    className={cn(
                        "flex-1 flex flex-col items-center py-3 space-y-1 rounded-none",
                        activeView === "main"
                            ? "text-black dark:text-white bg-gray-100 dark:bg-gray-900"
                            : "text-gray-500 hover:text-black dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-900/50"
                    )}
                    onClick={() => setActiveView("main")}
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M9 18V5l12-2v13" />
                        <circle cx="6" cy="18" r="3" />
                        <circle cx="18" cy="16" r="3" />
                    </svg>
                    <span className="text-xs">Generate</span>
                </Button>

                <Button
                    variant="ghost"
                    className={cn(
                        "flex-1 flex flex-col items-center py-3 space-y-1 rounded-none",
                        activeView === "settings"
                            ? "text-black dark:text-white bg-gray-100 dark:bg-gray-900"
                            : "text-gray-500 hover:text-black dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-900/50"
                    )}
                    onClick={() => setActiveView("settings")}
                >
                    <Settings className="w-5 h-5" />
                    <span className="text-xs">Settings</span>
                </Button>

                <Button
                    variant="ghost"
                    className={cn(
                        "flex-1 flex flex-col items-center py-3 space-y-1 rounded-none",
                        activeView === "history"
                            ? "text-black dark:text-white bg-gray-100 dark:bg-gray-900"
                            : "text-gray-500 hover:text-black dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-900/50"
                    )}
                    onClick={() => setActiveView("history")}
                >
                    <History className="w-5 h-5" />
                    <span className="text-xs">History</span>
                </Button>
            </div>
        </div>
    );
}
