import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { CustomLogger } from '../services/logger.service';

interface RateLimitInfo {
  count: number;
  resetTime: number;
  firstRequest: number;
}

@Injectable()
export class RateLimitMiddleware implements NestMiddleware {
  private requests = new Map<string, RateLimitInfo>();
  private readonly windowMs = 15 * 60 * 1000; // 15分钟窗口
  private readonly maxRequests = 1000; // 每个窗口最大请求数
  private readonly cleanupInterval = 5 * 60 * 1000; // 5分钟清理一次
  private middlewareLogger: CustomLogger;

  constructor(private readonly logger: CustomLogger) {
    this.middlewareLogger = this.logger.createLoggerWithContext('RateLimitMiddleware');
    
    // 定期清理过期记录
    setInterval(() => {
      this.cleanup();
    }, this.cleanupInterval);
  }

  use(req: Request, res: Response, next: NextFunction) {
    const clientIP = this.getClientIP(req);
    const now = Date.now();
    
    // 获取或创建客户端记录
    let rateLimitInfo = this.requests.get(clientIP);
    
    if (!rateLimitInfo || now > rateLimitInfo.resetTime) {
      // 创建新的速率限制记录
      rateLimitInfo = {
        count: 1,
        resetTime: now + this.windowMs,
        firstRequest: now,
      };
      this.requests.set(clientIP, rateLimitInfo);
    } else {
      // 增加请求计数
      rateLimitInfo.count++;
    }

    // 设置响应头
    res.set({
      'X-RateLimit-Limit': this.maxRequests.toString(),
      'X-RateLimit-Remaining': Math.max(0, this.maxRequests - rateLimitInfo.count).toString(),
      'X-RateLimit-Reset': new Date(rateLimitInfo.resetTime).toISOString(),
    });

    // 检查是否超过限制
    if (rateLimitInfo.count > this.maxRequests) {
      this.middlewareLogger.warn(`Rate limit exceeded for IP: ${clientIP}, requests: ${rateLimitInfo.count}`);
      
      res.status(429).json({
        code: 429,
        message: 'Too many requests, please try again later',
        data: null,
        retryAfter: Math.ceil((rateLimitInfo.resetTime - now) / 1000),
      });
      return;
    }

    next();
  }

  private getClientIP(req: Request): string {
    // 尝试多种方式获取真实IP
    return (
      req.headers['x-forwarded-for'] as string ||
      req.headers['x-real-ip'] as string ||
      req.connection?.remoteAddress ||
      req.socket?.remoteAddress ||
      'unknown'
    );
  }

  private cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [ip, info] of this.requests.entries()) {
      if (now > info.resetTime) {
        this.requests.delete(ip);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      this.middlewareLogger.log(`Cleaned up ${cleanedCount} expired rate limit records`);
    }
  }

  // 获取当前统计信息（用于监控）
  getStats(): { totalIPs: number; activeRequests: number } {
    const now = Date.now();
    let activeRequests = 0;
    
    for (const info of this.requests.values()) {
      if (now <= info.resetTime) {
        activeRequests += info.count;
      }
    }
    
    return {
      totalIPs: this.requests.size,
      activeRequests,
    };
  }
}
