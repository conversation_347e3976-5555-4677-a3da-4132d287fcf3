import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CustomLogger } from './logger.service';

interface EnvValidationRule {
  key: string;
  required: boolean;
  minLength?: number;
  pattern?: RegExp;
  description: string;
}

@Injectable()
export class EnvValidatorService {
  private validatorLogger: CustomLogger;

  // 定义环境变量验证规则
  private readonly validationRules: EnvValidationRule[] = [
    {
      key: 'NODE_ENV',
      required: true,
      description: 'Application environment',
    },
    {
      key: 'API_KEY',
      required: true,
      minLength: 32,
      description: 'API authentication key',
    },
    {
      key: 'SUPABASE_URL',
      required: true,
      pattern: /^https:\/\/.+\.supabase\.co$/,
      description: 'Supabase project URL',
    },
    {
      key: 'SUPABASE_KEY',
      required: true,
      minLength: 100,
      description: 'Supabase service role key',
    },
    {
      key: 'STRIPE_SECRET_KEY',
      required: false, // 可选，取决于环境
      pattern: /^sk_(test_|live_)/,
      description: 'Stripe secret key',
    },
    {
      key: 'STRIPE_WEBHOOK_SECRET',
      required: false,
      pattern: /^whsec_/,
      description: 'Stripe webhook secret',
    },
  ];

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: CustomLogger,
  ) {
    this.validatorLogger = this.logger.createLoggerWithContext('EnvValidator');
  }

  /**
   * 验证所有环境变量
   */
  validateEnvironment(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const rule of this.validationRules) {
      const value = this.configService.get<string>(rule.key);
      const validation = this.validateSingleVar(rule, value);

      if (validation.error) {
        if (rule.required) {
          errors.push(validation.error);
        } else {
          warnings.push(validation.error);
        }
      }
    }

    // 检查生产环境特殊要求
    if (this.isProduction()) {
      const prodValidation = this.validateProductionEnvironment();
      errors.push(...prodValidation.errors);
      warnings.push(...prodValidation.warnings);
    }

    // 记录验证结果
    if (errors.length > 0) {
      this.validatorLogger.error(`Environment validation failed: ${errors.join(', ')}`);
    }

    if (warnings.length > 0) {
      this.validatorLogger.warn(`Environment validation warnings: ${warnings.join(', ')}`);
    }

    if (errors.length === 0 && warnings.length === 0) {
      this.validatorLogger.log('Environment validation passed');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 验证单个环境变量
   */
  private validateSingleVar(
    rule: EnvValidationRule,
    value: string | undefined,
  ): { error?: string } {
    // 检查必需变量
    if (rule.required && !value) {
      return {
        error: `Missing required environment variable: ${rule.key} (${rule.description})`,
      };
    }

    if (!value) {
      return {}; // 可选变量且未设置，跳过验证
    }

    // 检查最小长度
    if (rule.minLength && value.length < rule.minLength) {
      return {
        error: `${rule.key} must be at least ${rule.minLength} characters long`,
      };
    }

    // 检查模式匹配
    if (rule.pattern && !rule.pattern.test(value)) {
      return {
        error: `${rule.key} format is invalid (${rule.description})`,
      };
    }

    // 检查是否使用默认值
    if (this.isDefaultValue(rule.key, value)) {
      return {
        error: `${rule.key} is using a default/demo value, please set a secure value`,
      };
    }

    return {};
  }

  /**
   * 验证生产环境特殊要求
   */
  private validateProductionEnvironment(): {
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查Stripe配置
    const stripeSecretKey = this.configService.get<string>('STRIPE_SECRET_KEY');
    if (stripeSecretKey?.startsWith('sk_test_')) {
      warnings.push('Using Stripe test key in production environment');
    }

    // 检查调试模式
    const debugMode = this.configService.get<string>('DEBUG');
    if (debugMode === 'true') {
      warnings.push('Debug mode is enabled in production');
    }

    // 检查日志级别
    const logLevel = this.configService.get<string>('LOG_LEVEL');
    if (logLevel === 'debug') {
      warnings.push('Debug logging is enabled in production');
    }

    return { errors, warnings };
  }

  /**
   * 检查是否为默认值
   */
  private isDefaultValue(key: string, value: string): boolean {
    const defaultValues = {
      API_KEY: ['demo_api_key', 'your_api_key_here', 'changeme'],
      SUPABASE_KEY: ['your_supabase_key_here'],
      STRIPE_SECRET_KEY: ['sk_test_your_key_here'],
    };

    const defaults = defaultValues[key as keyof typeof defaultValues];
    return defaults ? defaults.includes(value) : false;
  }

  /**
   * 检查是否为生产环境
   */
  private isProduction(): boolean {
    return this.configService.get<string>('NODE_ENV') === 'production';
  }

  /**
   * 获取环境配置摘要（不包含敏感信息）
   */
  getEnvironmentSummary(): Record<string, any> {
    return {
      nodeEnv: this.configService.get<string>('NODE_ENV'),
      hasApiKey: !!this.configService.get<string>('API_KEY'),
      hasSupabaseConfig: !!(
        this.configService.get<string>('SUPABASE_URL') &&
        this.configService.get<string>('SUPABASE_KEY')
      ),
      hasStripeConfig: !!this.configService.get<string>('STRIPE_SECRET_KEY'),
      timestamp: new Date().toISOString(),
    };
  }
}
