import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CustomLogger } from '../services/logger.service';

@Injectable()
export class ApiKeyGuard implements CanActivate {
    private readonly apiKey: string;
    private readonly minKeyLength = 32; // 最小密钥长度
    private guardLogger: CustomLogger;
    private failedAttempts = new Map<string, { count: number; lastAttempt: number }>();
    private readonly maxFailedAttempts = 5;
    private readonly lockoutDuration = 15 * 60 * 1000; // 15分钟锁定

    constructor(
        private readonly configService: ConfigService,
        private readonly logger: CustomLogger,
    ) {
        this.apiKey = this.configService.get<string>('API_KEY');
        this.guardLogger = this.logger.createLoggerWithContext(ApiKeyGuard.name);

        // 验证API密钥配置
        this.validateApiKeyConfiguration();
    }

    private validateApiKeyConfiguration(): void {
        if (!this.apiKey) {
            throw new Error('API_KEY environment variable is required');
        }

        if (this.apiKey === 'demo_api_key') {
            this.guardLogger.warn('Using demo API key - this is not secure for production!');
        }

        if (this.apiKey.length < this.minKeyLength) {
            throw new Error(`API key must be at least ${this.minKeyLength} characters long`);
        }
    }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const clientIP = this.getClientIP(request);
        const apiKey = this.extractApiKey(request);

        // 检查IP是否被锁定
        if (this.isIPLocked(clientIP)) {
            this.guardLogger.warn(`IP ${clientIP} is locked due to too many failed attempts`);
            throw new UnauthorizedException('Access temporarily blocked');
        }

        // 验证API密钥
        if (!apiKey || !this.isValidApiKey(apiKey)) {
            this.recordFailedAttempt(clientIP);
            // 不在日志中记录实际的API密钥值
            this.guardLogger.warn(`Invalid API key attempt from IP: ${clientIP}`);
            throw new UnauthorizedException('Invalid API key');
        }

        // 成功验证，清除失败记录
        this.clearFailedAttempts(clientIP);
        return true;
    }

    private isValidApiKey(providedKey: string): boolean {
        // 使用时间安全的字符串比较，防止时序攻击
        if (providedKey.length !== this.apiKey.length) {
            return false;
        }

        let result = 0;
        for (let i = 0; i < this.apiKey.length; i++) {
            result |= this.apiKey.charCodeAt(i) ^ providedKey.charCodeAt(i);
        }

        return result === 0;
    }

    private getClientIP(request: any): string {
        return request.ip ||
            request.connection?.remoteAddress ||
            request.socket?.remoteAddress ||
            'unknown';
    }

    private isIPLocked(ip: string): boolean {
        const attempts = this.failedAttempts.get(ip);
        if (!attempts) return false;

        const now = Date.now();
        if (now - attempts.lastAttempt > this.lockoutDuration) {
            this.failedAttempts.delete(ip);
            return false;
        }

        return attempts.count >= this.maxFailedAttempts;
    }

    private recordFailedAttempt(ip: string): void {
        const now = Date.now();
        const attempts = this.failedAttempts.get(ip) || { count: 0, lastAttempt: 0 };

        // 如果距离上次失败超过锁定时间，重置计数
        if (now - attempts.lastAttempt > this.lockoutDuration) {
            attempts.count = 1;
        } else {
            attempts.count++;
        }

        attempts.lastAttempt = now;
        this.failedAttempts.set(ip, attempts);
    }

    private clearFailedAttempts(ip: string): void {
        this.failedAttempts.delete(ip);
    }

    private extractApiKey(request: any): string | null {
        // 首先尝试从请求头中获取
        const apiKey = request.headers['x-api-key'];
        if (apiKey) {
            return apiKey;
        }

        // 然后尝试从查询参数中获取
        if (request.query && request.query.api_key) {
            return request.query.api_key;
        }

        // 最后尝试从请求体中获取
        if (request.body && request.body.api_key) {
            return request.body.api_key;
        }

        return null;
    }
} 