import "./instrument";

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import * as bodyParser from 'body-parser';
import { STRIPE_WEBHOOK_MODULE, STRIPE_WEBHOOK_ROUTE } from './common/constants/route';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import helmet from 'helmet';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    bodyParser: false,  // 禁用内置的bodyParser
  });

  // 安全头部配置 - 使用 helmet 中间件
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'", "https://api.stripe.com"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    crossOriginEmbedderPolicy: false, // 避免与某些API冲突
  }));

  const stripeWebhookRoute = `${STRIPE_WEBHOOK_MODULE}${STRIPE_WEBHOOK_ROUTE}`;

  // 为Stripe webhook路由配置原始请求体处理
  app.use(stripeWebhookRoute,
    bodyParser.raw({ type: 'application/json', limit: '1mb' }) // 限制请求体大小
  );

  // 为其他路由配置JSON解析，添加大小限制
  app.use((req, res, next) => {
    if (req.url.includes(stripeWebhookRoute)) {
      next();
    } else {
      bodyParser.json({
        limit: '10mb', // 限制JSON请求体大小
        verify: (req, res, buf) => {
          // 验证请求体不为空且格式正确
          if (buf && buf.length === 0) {
            throw new Error('Empty request body');
          }
        }
      })(req, res, next);
    }
  });

  // 添加CORS配置 - 更严格的配置
  app.enableCors({
    origin: (origin, callback) => {
      // 允许的域名列表
      const allowedOrigins = [
        'http://localhost:3001',
        'http://localhost:3000',
        'http://localhost:5173',
        /^https:\/\/.*\.reelmind\.ai$/,
        'https://reelmind.ai'
      ];

      // 开发环境允许无origin的请求（如Postman）
      if (!origin && process.env.NODE_ENV === 'development') {
        return callback(null, true);
      }

      // 检查origin是否在允许列表中
      const isAllowed = allowedOrigins.some(allowed => {
        if (typeof allowed === 'string') {
          return allowed === origin;
        }
        return allowed.test(origin || '');
      });

      callback(null, isAllowed);
    },
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
    maxAge: 86400, // 预检请求缓存时间
  });

  // 加强输入验证配置
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true, // 去除未定义的属性 - 启用安全性
    transform: true, // 自动转换类型
    forbidNonWhitelisted: true, // 禁止未定义的属性 - 启用安全性
    disableErrorMessages: process.env.NODE_ENV === 'production', // 生产环境隐藏详细错误
    transformOptions: {
      enableImplicitConversion: true, // 启用隐式转换
    },
    exceptionFactory: (errors) => {
      // 自定义验证错误格式，避免泄露敏感信息
      const messages = errors.map(error => {
        return `${error.property}: ${Object.values(error.constraints || {}).join(', ')}`;
      });
      return new Error(`Validation failed: ${messages.join('; ')}`);
    },
  }));

  // 配置 Swagger 文档 - 仅在开发环境启用
  if (process.env.NODE_ENV !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('ReelMind API')
      .setDescription('ReelMind 平台 API 文档')
      .setVersion('1.0')
      .addBearerAuth()
      .build();
    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document);
    console.log(`Swagger documentation is available at: http://localhost:${process.env.PORT ?? 3000}/api/docs`);
  }

  // 设置服务器超时
  const server = await app.listen(process.env.PORT ?? 3000);
  server.setTimeout(30000); // 30秒超时

  console.log('Environment:', process.env.NODE_ENV);
  console.log(`Application is running on: http://localhost:${process.env.PORT ?? 3000}`);

  // 生产环境不输出敏感信息
  if (process.env.NODE_ENV === 'development') {
    console.log('Development mode - additional logging enabled');
  }
}

bootstrap();
