"use client";

import { useState, useCallback, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useSoundStore, SoundTask } from "@/store/useSoundStore";
import { useSoundHistory } from "../hooks/useSoundHistory";
import {
    Play,
    Pause,
    Download,
    Copy,
    MoreHorizontal,
    Clock,
    CheckCircle,
    XCircle,
    Loader2,
    History
} from "lucide-react";
import { cn } from "@/lib/utils";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";



interface HistoryCardProps {
    task: SoundTask;
    onPlay: (task: SoundTask) => void;
    onDownload: (task: SoundTask) => void;
    onCopyText: (text: string) => void;
    isPlaying: boolean;
}

function HistoryCard({ task, onPlay, onDownload, onCopyText, isPlaying }: HistoryCardProps) {
    const getStatusIcon = () => {
        switch (task.status) {
            case "completed":
                return <CheckCircle className="w-4 h-4 text-green-400" />;
            case "processing":
                return <Loader2 className="w-4 h-4 text-blue-400 animate-spin" />;
            case "failed":
                return <XCircle className="w-4 h-4 text-red-400" />;
            default:
                return <Clock className="w-4 h-4 text-yellow-400" />;
        }
    };

    const getStatusText = () => {
        switch (task.status) {
            case "completed":
                return "Completed";
            case "processing":
                return "Processing...";
            case "failed":
                return "Failed";
            default:
                return "Pending";
        }
    };

    const formatTime = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffMins = Math.floor(diffMs / (1000 * 60));

        if (diffMins < 1) return "Just now";
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
        return date.toLocaleDateString();
    };

    return (
        <Card className="bg-gray-800/30 border-gray-700 hover:bg-gray-800/50 transition-colors">
            <CardContent className="p-4">
                <div className="space-y-3">
                    {/* Header */}
                    <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-2">
                            {getStatusIcon()}
                            <span className="text-xs text-gray-400">{getStatusText()}</span>
                            <span className="text-xs text-gray-500">•</span>
                            <span className="text-xs text-gray-500">{formatTime(task.created_at)}</span>
                        </div>

                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-gray-400 hover:text-white">
                                    <MoreHorizontal className="w-4 h-4" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="bg-gray-800 border-gray-600">
                                <DropdownMenuItem
                                    onClick={() => onCopyText(task.text)}
                                    className="text-white hover:bg-gray-700"
                                >
                                    <Copy className="w-4 h-4 mr-2" />
                                    Copy Text
                                </DropdownMenuItem>
                                {task.status === "completed" && task.audio_url && (
                                    <DropdownMenuItem
                                        onClick={() => onDownload(task)}
                                        className="text-white hover:bg-gray-700"
                                    >
                                        <Download className="w-4 h-4 mr-2" />
                                        Download
                                    </DropdownMenuItem>
                                )}
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>

                    {/* Text Content */}
                    <div className="text-sm text-gray-300 line-clamp-3">
                        {task.text}
                    </div>

                    {/* Voice Info */}
                    <div className="text-xs text-gray-500">
                        {task.voice_model_name} • Speed: {task.speed}x • Stability: {(task.stability * 100).toFixed(0)}%
                    </div>

                    {/* Error Message */}
                    {task.status === "failed" && task.error_message && (
                        <div className="text-xs text-red-400 bg-red-900/20 p-2 rounded">
                            {task.error_message}
                        </div>
                    )}

                    {/* Play Button */}
                    {task.status === "completed" && task.audio_url && (
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onPlay(task)}
                            className="w-full bg-gray-900/50 border-gray-600 text-white hover:bg-gray-700"
                        >
                            {isPlaying ? (
                                <>
                                    <Pause className="w-3 h-3 mr-2" />
                                    Pause
                                </>
                            ) : (
                                <>
                                    <Play className="w-3 h-3 mr-2" />
                                    Play
                                </>
                            )}
                        </Button>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}

export function HistoryPanel() {
    const { soundHistory, isLoadingHistory, hasMoreHistory, loadMoreHistory } = useSoundHistory();
    const [currentlyPlaying, setCurrentlyPlaying] = useState<string | null>(null);
    const audioRef = useRef<HTMLAudioElement | null>(null);

    const handlePlay = useCallback((task: SoundTask) => {
        if (!task.audio_url) return;

        if (currentlyPlaying === task.id) {
            // Pause current audio
            if (audioRef.current) {
                audioRef.current.pause();
            }
            setCurrentlyPlaying(null);
        } else {
            // Play new audio
            if (audioRef.current) {
                audioRef.current.pause();
            }

            // Create new audio element
            const audio = new Audio(task.audio_url);
            audioRef.current = audio;

            audio.onended = () => {
                setCurrentlyPlaying(null);
            };

            audio.onerror = () => {
                setCurrentlyPlaying(null);
                console.error("Failed to play audio");
            };

            audio.play().then(() => {
                setCurrentlyPlaying(task.id);
            }).catch((error) => {
                console.error("Audio play failed:", error);
                setCurrentlyPlaying(null);
            });
        }
    }, [currentlyPlaying]);

    const handleDownload = useCallback((task: SoundTask) => {
        if (!task.audio_url) return;

        const link = document.createElement('a');
        link.href = task.audio_url;
        link.download = `voice-${task.id}.mp3`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }, []);

    const handleCopyText = useCallback((text: string) => {
        navigator.clipboard.writeText(text).then(() => {
            // TODO: Show toast notification
            console.log("Text copied to clipboard");
        }).catch((error) => {
            console.error("Failed to copy text:", error);
        });
    }, []);

    return (
        <div className="w-80 bg-gray-900/20 border-l border-gray-800 flex flex-col">
            {/* Header */}
            <div className="p-4 border-b border-gray-800">
                <div className="flex items-center space-x-2">
                    <History className="w-5 h-5 text-white" />
                    <h2 className="text-lg font-semibold text-white">History</h2>
                </div>
            </div>

            {/* History List */}
            <div className="flex-1 overflow-y-auto p-4 space-y-3">
                {soundHistory.length === 0 ? (
                    <div className="text-center py-8">
                        <History className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                        <p className="text-gray-400 text-sm">No voice generations yet</p>
                        <p className="text-gray-500 text-xs mt-1">Your generated voices will appear here</p>
                    </div>
                ) : (
                    soundHistory.map((task) => (
                        <HistoryCard
                            key={task.id}
                            task={task}
                            onPlay={handlePlay}
                            onDownload={handleDownload}
                            onCopyText={handleCopyText}
                            isPlaying={currentlyPlaying === task.id}
                        />
                    ))
                )}

                {/* Load More Button */}
                {hasMoreHistory && (
                    <Button
                        variant="outline"
                        className="w-full bg-gray-800/50 border-gray-600 text-white hover:bg-gray-700"
                        disabled={isLoadingHistory}
                        onClick={loadMoreHistory}
                    >
                        {isLoadingHistory ? (
                            <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Loading...
                            </>
                        ) : (
                            "Load More"
                        )}
                    </Button>
                )}
            </div>
        </div>
    );
}
