"use client";

import { useSoundStore, SoundType } from "@/store/useSoundStore";
import { cn } from "@/lib/utils";
import { Volume2, AudioWaveform } from "lucide-react";

export function FunctionToggle() {
    const { soundType, setSoundType } = useSoundStore();

    const toggleOptions: { type: SoundType; label: string; icon: React.ComponentType<{ className?: string }> }[] = [
        {
            type: 'text-to-speech',
            label: 'Text to Speech',
            icon: Volume2,
        },
        {
            type: 'sound-effect',
            label: 'Sound Effect',
            icon: AudioWaveform,
        },
    ];

    return (
        <div className="flex items-center justify-center mb-8">
            <div className="relative bg-gray-100 dark:bg-gray-800 rounded-full p-1 flex">
                {toggleOptions.map((option) => {
                    const Icon = option.icon;
                    const isActive = soundType === option.type;

                    return (
                        <button
                            key={option.type}
                            onClick={() => setSoundType(option.type)}
                            className={cn(
                                "relative flex items-center space-x-2 px-6 py-3 rounded-full transition-all duration-300 ease-in-out",
                                "text-sm font-medium whitespace-nowrap",
                                isActive
                                    ? "bg-white dark:bg-black text-black dark:text-white shadow-sm"
                                    : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                            )}
                        >
                            <Icon className="w-4 h-4" />
                            <span>{option.label}</span>
                        </button>
                    );
                })}
            </div>
        </div>
    );
}
