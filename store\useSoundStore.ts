import { create } from 'zustand';

// Voice model interface
export interface VoiceModel {
    id: string;
    name: string;
    description: string;
    language: string;
    gender: 'male' | 'female' | 'neutral';
    preview_url?: string;
}

// Sound generation task interface
export interface SoundTask {
    id: string;
    text: string;
    voice_model_id: string;
    voice_model_name: string;
    speed: number;
    stability: number;
    similarity_boost: number;
    style_exaggeration: number;
    audio_url?: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    created_at: string;
    error_message?: string;
}

interface SoundState {
    // Text input
    text: string;
    characterCount: number;
    maxCharacters: number;
    
    // Voice settings
    selectedVoiceModel: VoiceModel | null;
    availableVoiceModels: VoiceModel[];
    speed: number; // 0.25 - 4.0
    stability: number; // 0 - 1
    similarityBoost: number; // 0 - 1
    styleExaggeration: number; // 0 - 1
    
    // Generation state
    isGenerating: boolean;
    currentTask: SoundTask | null;
    error: string | null;
    
    // History
    soundHistory: SoundTask[];
    isLoadingHistory: boolean;
    hasMoreHistory: boolean;
    
    // UI state
    isSidebarCollapsed: boolean;
    
    // Actions
    setText: (text: string) => void;
    setSelectedVoiceModel: (model: VoiceModel | null) => void;
    setSpeed: (speed: number) => void;
    setStability: (stability: number) => void;
    setSimilarityBoost: (boost: number) => void;
    setStyleExaggeration: (exaggeration: number) => void;
    setIsGenerating: (generating: boolean) => void;
    setCurrentTask: (task: SoundTask | null) => void;
    setError: (error: string | null) => void;
    setSoundHistory: (history: SoundTask[]) => void;
    addToHistory: (task: SoundTask) => void;
    setIsLoadingHistory: (loading: boolean) => void;
    setHasMoreHistory: (hasMore: boolean) => void;
    setIsSidebarCollapsed: (collapsed: boolean) => void;
    setAvailableVoiceModels: (models: VoiceModel[]) => void;
    resetSettings: () => void;
}

export const useSoundStore = create<SoundState>((set, get) => ({
    // Initial state
    text: '',
    characterCount: 0,
    maxCharacters: 5000,
    
    selectedVoiceModel: null,
    availableVoiceModels: [],
    speed: 1.0,
    stability: 0.5,
    similarityBoost: 0.5,
    styleExaggeration: 0.0,
    
    isGenerating: false,
    currentTask: null,
    error: null,
    
    soundHistory: [],
    isLoadingHistory: false,
    hasMoreHistory: true,
    
    isSidebarCollapsed: false,
    
    // Actions
    setText: (text: string) => {
        set({ 
            text, 
            characterCount: text.length,
            error: text.length > get().maxCharacters ? `Text exceeds ${get().maxCharacters} characters` : null
        });
    },
    
    setSelectedVoiceModel: (model: VoiceModel | null) => {
        set({ selectedVoiceModel: model });
    },
    
    setSpeed: (speed: number) => {
        set({ speed: Math.max(0.25, Math.min(4.0, speed)) });
    },
    
    setStability: (stability: number) => {
        set({ stability: Math.max(0, Math.min(1, stability)) });
    },
    
    setSimilarityBoost: (boost: number) => {
        set({ similarityBoost: Math.max(0, Math.min(1, boost)) });
    },
    
    setStyleExaggeration: (exaggeration: number) => {
        set({ styleExaggeration: Math.max(0, Math.min(1, exaggeration)) });
    },
    
    setIsGenerating: (generating: boolean) => {
        set({ isGenerating: generating });
    },
    
    setCurrentTask: (task: SoundTask | null) => {
        set({ currentTask: task });
    },
    
    setError: (error: string | null) => {
        set({ error });
    },
    
    setSoundHistory: (history: SoundTask[]) => {
        set({ soundHistory: history });
    },
    
    addToHistory: (task: SoundTask) => {
        set(state => ({ 
            soundHistory: [task, ...state.soundHistory] 
        }));
    },
    
    setIsLoadingHistory: (loading: boolean) => {
        set({ isLoadingHistory: loading });
    },
    
    setHasMoreHistory: (hasMore: boolean) => {
        set({ hasMoreHistory: hasMore });
    },
    
    setIsSidebarCollapsed: (collapsed: boolean) => {
        set({ isSidebarCollapsed: collapsed });
    },
    
    setAvailableVoiceModels: (models: VoiceModel[]) => {
        set({ availableVoiceModels: models });
    },
    
    resetSettings: () => {
        set({
            speed: 1.0,
            stability: 0.5,
            similarityBoost: 0.5,
            styleExaggeration: 0.0,
        });
    },
}));
