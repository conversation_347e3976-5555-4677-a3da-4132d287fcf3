import { SoundTask, VoiceModel } from "@/store/useSoundStore";

// API endpoints
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

// Sound generation request interface
export interface SoundGenerationRequest {
    text: string;
    voice_model_id: string;
    speed: number;
    stability: number;
    similarity_boost: number;
    style_exaggeration: number;
}

// API response interfaces
export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}

export interface SoundGenerationResponse {
    task_id: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    audio_url?: string;
    error_message?: string;
}

export interface SoundHistoryResponse {
    tasks: SoundTask[];
    total: number;
    has_more: boolean;
}

export interface VoiceModelsResponse {
    models: VoiceModel[];
}

// Sound API class
export class SoundApi {
    private baseUrl: string;

    constructor(baseUrl: string = API_BASE_URL) {
        this.baseUrl = baseUrl;
    }

    private async request<T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<ApiResponse<T>> {
        try {
            const url = `${this.baseUrl}${endpoint}`;
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers,
                },
                ...options,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
            };
        }
    }

    // Get available voice models
    async getVoiceModels(): Promise<ApiResponse<VoiceModelsResponse>> {
        return this.request<VoiceModelsResponse>('/api/sound/models');
    }

    // Generate speech from text
    async generateSpeech(request: SoundGenerationRequest): Promise<ApiResponse<SoundGenerationResponse>> {
        return this.request<SoundGenerationResponse>('/api/sound/generate', {
            method: 'POST',
            body: JSON.stringify(request),
        });
    }

    // Get task status
    async getTaskStatus(taskId: string): Promise<ApiResponse<SoundTask>> {
        return this.request<SoundTask>(`/api/sound/tasks/${taskId}`);
    }

    // Get user's sound generation history
    async getSoundHistory(
        limit: number = 20,
        offset: number = 0
    ): Promise<ApiResponse<SoundHistoryResponse>> {
        const params = new URLSearchParams({
            limit: limit.toString(),
            offset: offset.toString(),
        });
        
        return this.request<SoundHistoryResponse>(`/api/sound/history?${params}`);
    }

    // Delete a sound task
    async deleteTask(taskId: string): Promise<ApiResponse<void>> {
        return this.request<void>(`/api/sound/tasks/${taskId}`, {
            method: 'DELETE',
        });
    }

    // Get audio download URL
    async getDownloadUrl(taskId: string): Promise<ApiResponse<{ download_url: string }>> {
        return this.request<{ download_url: string }>(`/api/sound/tasks/${taskId}/download`);
    }
}

// Create singleton instance
export const soundApi = new SoundApi();

// Utility functions
export const pollTaskStatus = async (
    taskId: string,
    onUpdate: (task: SoundTask) => void,
    maxAttempts: number = 60,
    interval: number = 2000
): Promise<SoundTask> => {
    let attempts = 0;

    return new Promise((resolve, reject) => {
        const poll = async () => {
            try {
                const response = await soundApi.getTaskStatus(taskId);
                
                if (!response.success || !response.data) {
                    throw new Error(response.error || 'Failed to get task status');
                }

                const task = response.data;
                onUpdate(task);

                if (task.status === 'completed' || task.status === 'failed') {
                    resolve(task);
                    return;
                }

                attempts++;
                if (attempts >= maxAttempts) {
                    reject(new Error('Polling timeout: Task did not complete in time'));
                    return;
                }

                setTimeout(poll, interval);
            } catch (error) {
                reject(error);
            }
        };

        poll();
    });
};

// Error handling utilities
export const handleApiError = (error: any): string => {
    if (typeof error === 'string') {
        return error;
    }
    
    if (error?.message) {
        return error.message;
    }
    
    if (error?.error) {
        return error.error;
    }
    
    return 'An unexpected error occurred';
};

// Validation utilities
export const validateSoundRequest = (request: SoundGenerationRequest): string | null => {
    if (!request.text || request.text.trim().length === 0) {
        return 'Text is required';
    }
    
    if (request.text.length > 5000) {
        return 'Text must be less than 5000 characters';
    }
    
    if (!request.voice_model_id) {
        return 'Voice model is required';
    }
    
    if (request.speed < 0.25 || request.speed > 4.0) {
        return 'Speed must be between 0.25 and 4.0';
    }
    
    if (request.stability < 0 || request.stability > 1) {
        return 'Stability must be between 0 and 1';
    }
    
    if (request.similarity_boost < 0 || request.similarity_boost > 1) {
        return 'Similarity boost must be between 0 and 1';
    }
    
    if (request.style_exaggeration < 0 || request.style_exaggeration > 1) {
        return 'Style exaggeration must be between 0 and 1';
    }
    
    return null;
};
