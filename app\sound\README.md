# Sound Studio - AI Voice Generation

A comprehensive AI voice generation interface inspired by ElevenLabs, built with Next.js, TypeScript, and Tailwind CSS.

## Features

### 🎙️ Voice Generation
- **Text-to-Speech**: Convert any text to high-quality AI-generated speech
- **Multiple Voice Models**: Choose from various voice personalities and styles
- **Advanced Controls**: Fine-tune speed, stability, similarity boost, and style exaggeration
- **Real-time Character Count**: Track text length with visual feedback
- **Error Handling**: Comprehensive validation and error messages

### 🎛️ Settings Panel
- **Voice Selection**: Browse and select from available voice models with descriptions
- **Model Configuration**: Choose between different AI models (Eleven Multilingual v2, Eleven Turbo v2)
- **Speed Control**: Adjust playback speed from 0.25x to 4.0x
- **Stability Control**: Control voice consistency (0-100%)
- **Similarity Boost**: Enhance voice similarity to original (0-100%)
- **Style Exaggeration**: Adjust emotional expression (0-100%)
- **Voice Preview**: Play sample audio for each voice model
- **Reset Settings**: Quick reset to default values

### 📚 History Management
- **Generation History**: View all previous voice generations
- **Status Tracking**: Real-time status updates (pending, processing, completed, failed)
- **Audio Playback**: Play generated audio directly in the interface
- **Download Support**: Download generated audio files
- **Text Copy**: Copy original text for reuse
- **Load More**: Infinite scroll for large history lists
- **Error Display**: Clear error messages for failed generations

### 🎨 User Interface
- **Dark Theme**: Modern dark interface with subtle gradients
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Mobile Layout**: Dedicated mobile interface with bottom navigation
- **Sidebar Navigation**: Collapsible sidebar with voice generation tools
- **Loading States**: Smooth loading animations and skeleton screens
- **Toast Notifications**: User feedback for actions and errors

### 📱 Responsive Layout
- **Desktop**: Full three-panel layout (sidebar, main, settings, history)
- **Tablet**: Adaptive layout with collapsible panels
- **Mobile**: Bottom navigation with swipeable views
- **Touch Optimized**: Mobile-friendly controls and interactions

## Technical Architecture

### 🏗️ Component Structure
```
app/sound/
├── page.tsx                    # Main page component
├── layout.tsx                  # Layout with metadata
├── components/
│   ├── sound-page-content.tsx  # Main page content with responsive logic
│   ├── main-operation-panel.tsx # Text input and generation controls
│   ├── settings-panel.tsx      # Voice and parameter settings
│   ├── history-panel.tsx       # Generation history display
│   ├── sound-sidebar.tsx       # Navigation sidebar
│   └── mobile-sound-layout.tsx # Mobile-specific layout
├── hooks/
│   ├── useSoundGeneration.ts   # Speech generation logic
│   ├── useSoundHistory.ts      # History management
│   └── useVoiceModels.ts       # Voice model management
└── README.md                   # This file
```

### 🔧 State Management
- **Zustand Store**: Centralized state management for all sound-related data
- **Custom Hooks**: Separation of concerns with dedicated hooks for different features
- **API Integration**: RESTful API calls with error handling and retry logic
- **Real-time Updates**: Polling for generation status updates

### 🎯 Key Technologies
- **Next.js 15**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **Zustand**: Lightweight state management
- **Radix UI**: Accessible component primitives
- **Lucide React**: Modern icon library

## API Integration

### Endpoints
- `GET /api/sound/models` - Get available voice models
- `POST /api/sound/generate` - Start speech generation
- `GET /api/sound/tasks/{id}` - Get generation status
- `GET /api/sound/history` - Get user's generation history
- `GET /api/sound/tasks/{id}/download` - Get download URL
- `DELETE /api/sound/tasks/{id}` - Delete generation

### Data Flow
1. User enters text and selects voice settings
2. Validation occurs on the client side
3. Generation request sent to API
4. Real-time polling for status updates
5. Completed audio added to history
6. User can play, download, or share results

## Usage

### Basic Generation
1. Enter text in the main input area
2. Select a voice model from the settings panel
3. Adjust speed, stability, and other parameters as needed
4. Click "Generate Speech" to start the process
5. Monitor progress in the history panel
6. Play or download the completed audio

### Advanced Features
- **Voice Preview**: Click "Preview Voice" to hear a sample
- **Parameter Tuning**: Experiment with different settings for unique voices
- **History Management**: Access and manage all previous generations
- **Mobile Usage**: Full functionality available on mobile devices

## Design Philosophy

This interface follows the user's preferences for:
- **Zen-style minimalism**: Clean, uncluttered design with ample whitespace
- **Dark theme**: Professional dark interface with subtle accents
- **English-only UI**: All interface text in English
- **Responsive design**: Seamless experience across all devices
- **Accessibility**: Keyboard navigation and screen reader support

The design is inspired by ElevenLabs but adapted to fit the ReelMind design system and user preferences.
