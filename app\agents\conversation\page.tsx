"use client"

import { useState, useEffect } from "react"
import { ArrowR<PERSON>, Send, Plus, Wand2, Film, Camera, Video, Play, Lock, Key, CheckCircle2, Info, Clock, XCircle, Shield<PERSON>heck, <PERSON>rk<PERSON>, History } from "lucide-react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { qwenClient } from "@/lib/qwen-client"
import { createClient } from "@/lib/supabase/client"
import "../../../styles/coming-soon-animations.css"

interface SessionHistory {
  id: string
  prompt: string
  created_at: string
  status: string
}

export default function ConversationPage() {
  const router = useRouter()
  const [prompt, setPrompt] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [searchResult, setSearchResult] = useState<string | null>(null)
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)
  
  // Invitation code state
  const [invitationCode, setInvitationCode] = useState("")
  const [isCodeVerified, setIsCodeVerified] = useState(false)
  const [isCodeError, setIsCodeError] = useState(false)
  const [errorMessage, setErrorMessage] = useState("")
  const [isVerifying, setIsVerifying] = useState(false)
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false)
  const [isCheckingAccess, setIsCheckingAccess] = useState(true)
  
  // History state
  const [showHistory, setShowHistory] = useState(false)
  const [sessionHistory, setSessionHistory] = useState<SessionHistory[]>([])
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)

  const supabase = createClient()

  // Check user access on component mount
  useEffect(() => {
    checkUserAccess()
  }, [])

  const checkUserAccess = async () => {
    try {
      setIsCheckingAccess(true)
      
      // Check if user is authenticated
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        // Don't redirect to login, just show the invitation code overlay
        setIsCodeVerified(false)
        setIsCheckingAccess(false)
        return
      }

      // Check localStorage first to avoid unnecessary API calls
      try {
        const cachedAccess = localStorage.getItem('movie_agents_access')
        if (cachedAccess === 'granted') {
          setIsCodeVerified(true)
          setIsCheckingAccess(false)
          return
        }
      } catch (storageError) {
        // localStorage might not be available in some environments
        console.log('localStorage not available:', storageError)
      }

      // Check if user has access via API
      const response = await fetch('/api/movie-agents/invitation/verify')
      if (response.ok) {
        const data = await response.json()
        if (data.hasAccess) {
          setIsCodeVerified(true)
          // Cache the access state
          try {
            localStorage.setItem('movie_agents_access', 'granted')
          } catch (storageError) {
            console.log('Could not cache access state:', storageError)
          }
        } else {
          setIsCodeVerified(false)
          // Clear any stale cache
          try {
            localStorage.removeItem('movie_agents_access')
          } catch (storageError) {
            console.log('Could not clear cache:', storageError)
          }
        }
      }
    } catch (error) {
      console.error('Error checking access:', error)
      // Clear cache on error
      try {
        localStorage.removeItem('movie_agents_access')
      } catch (storageError) {
        console.log('Could not clear cache on error:', storageError)
      }
    } finally {
      setIsCheckingAccess(false)
    }
  }

  const loadSessionHistory = async () => {
    try {
      setIsLoadingHistory(true)
      const response = await fetch('/api/movie-agents/sessions?limit=10')
      if (response.ok) {
        const data = await response.json()
        setSessionHistory(data.sessions)
      }
    } catch (error) {
      console.error('Error loading history:', error)
    } finally {
      setIsLoadingHistory(false)
    }
  }

  const saveSession = async (sessionData: any) => {
    try {
      console.log('💾 尝试保存会话:', sessionData)
      const response = await fetch('/api/movie-agents/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sessionData),
      })
      
      if (response.ok) {
        const data = await response.json()
        console.log('✅ 会话保存成功:', data.session.id)
        setCurrentSessionId(data.session.id)
        return data.session.id
      } else {
        const errorData = await response.json()
        console.error('❌ 会话保存失败:', errorData)
        return null
      }
    } catch (error) {
      console.error('❌ 会话保存错误:', error)
      return null
    }
  }
  
  const handleSubmitPrompt = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!prompt.trim()) return
    
    console.log('🚀 提交prompt:', prompt)
    setIsSubmitting(true)
    
    try {
      // Call Qwen API for web search (but don't wait for it to complete navigation)
      const searchPromise = qwenClient.search(prompt);
      
      // Save initial session (but don't wait for it to complete navigation)
      const sessionPromise = saveSession({
        prompt: prompt,
        status: 'active'
      })
      
      // Navigate immediately without waiting for search or session save
      const params = new URLSearchParams({
        prompt: prompt
      })
      const navigationUrl = `/agents/think-plan?${params.toString()}`
      console.log('🔗 立即导航到:', navigationUrl)
      
      // Navigate immediately
      router.push(navigationUrl)
      
      // Handle search and session save in background
      try {
        const [searchResponse, sessionId] = await Promise.allSettled([searchPromise, sessionPromise])
        
        if (searchResponse.status === 'fulfilled') {
          console.log('🔍 搜索结果:', searchResponse.value.result)
          setSearchResult(searchResponse.value.result)
        }
        
        if (sessionId.status === 'fulfilled' && sessionId.value) {
          console.log('💾 会话保存成功:', sessionId.value)
          setCurrentSessionId(sessionId.value)
        }
      } catch (backgroundError) {
        console.log('⚠️ 后台处理完成，但有部分错误:', backgroundError)
      }
      
    } catch (error) {
      console.error("❌ 处理错误:", error);
      // Even if there's an error, still navigate to think-plan
      const params = new URLSearchParams({ prompt: prompt })
      const navigationUrl = `/agents/think-plan?${params.toString()}`
      console.log('🔗 错误后导航到:', navigationUrl)
      router.push(navigationUrl)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // Function to verify invitation code
  const verifyInvitationCode = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!invitationCode.trim()) return
    
    setIsVerifying(true)
    setIsCodeError(false)
    setErrorMessage("")
    
    try {
      const response = await fetch('/api/movie-agents/invitation/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: invitationCode }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        // Cache the access state
        try {
          localStorage.setItem('movie_agents_access', 'granted')
        } catch (storageError) {
          console.log('Could not cache access state:', storageError)
        }
        
        setShowSuccessAnimation(true)
        // Wait for animation to complete before removing overlay
        setTimeout(() => {
          setIsCodeVerified(true)
        }, 1500)
      } else {
        setIsCodeError(true)
        setErrorMessage(data.error || 'Invalid invitation code')
        setIsVerifying(false)
      }
    } catch (error) {
      console.error('Error verifying invitation code:', error)
      setIsCodeError(true)
      setErrorMessage('Network error. Please try again.')
      setIsVerifying(false)
    }
  }

  const loadHistorySession = (session: SessionHistory) => {
    router.push(`/agents/think-plan?sessionId=${session.id}`)
  }

  if (isCheckingAccess) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background/50 to-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4" />
          <p className="text-muted-foreground">Checking access permissions...</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-background/50 to-background flex flex-col overflow-x-hidden">
      {/* Clean Header Banner - Screenshot Style */}
      <header className="relative bg-white/95 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center relative">
            {/* Main Title Banner - Clean Style - Centered */}
            <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center justify-center gap-4">
              {/* Film Icon */}
              <div className="p-3 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg">
                <Film className="h-6 w-6 text-white" />
              </div>
              
              {/* Nolan Badge */}
              <div className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full shadow-lg">
                <span className="text-white font-bold text-lg">Nolan</span>
              </div>
              
              {/* Online Status */}
              <div className="flex items-center px-4 py-2 bg-green-50 rounded-full border border-green-200">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span className="text-green-700 text-sm font-medium">Online</span>
              </div>
            </div>
            
            {/* History Button - Right Side */}
            {isCodeVerified && (
              <div className="ml-auto flex items-center gap-3">
              <button
                onClick={() => {
                  setShowHistory(!showHistory)
                  if (!showHistory && sessionHistory.length === 0) {
                    loadSessionHistory()
                  }
                }}
                  className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2"
              >
                <History className="h-4 w-4" />
                History
              </button>
              </div>
            )}
          </div>
          
          {/* Subtitle - Centered below main title */}
          <div className="flex justify-center mt-4">
            <div className="relative inline-flex items-center">
              {/* Decorative elements */}
              <div className="absolute -left-2 top-1/2 transform -translate-y-1/2 w-1 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse"></div>
              <div className="absolute -right-2 top-1/2 transform -translate-y-1/2 w-1 h-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse delay-500"></div>
              
              {/* Main badge */}
              <div className="relative px-6 py-2 bg-gradient-to-r from-slate-50 via-blue-50 to-purple-50 rounded-full border border-gradient-to-r from-blue-200/50 to-purple-200/50 shadow-lg backdrop-blur-sm">
                {/* Shine effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full animate-pulse"></div>
                
                {/* Text with gradient */}
                <p className="relative text-sm font-bold bg-gradient-to-r from-slate-700 via-blue-600 to-purple-600 bg-clip-text text-transparent tracking-wide">
                  The World's First AI Agent Director
                </p>
                
                {/* Sparkle effects */}
                <div className="absolute -top-1 left-4 w-1 h-1 bg-blue-400 rounded-full animate-ping"></div>
                <div className="absolute -bottom-1 right-6 w-1 h-1 bg-purple-400 rounded-full animate-ping delay-700"></div>
              </div>
            </div>
          </div>
        </div>
      </header>
      
      {/* Main content */}
      <div className="flex-1 flex flex-col container mx-auto w-full px-4 py-4">
        <div className="flex-1 flex items-start justify-center pt-8">
          <Card className="w-full max-w-4xl shadow-lg border-primary/10">
            <CardContent className="p-6">
              <h2 className="text-2xl font-bold mb-6 text-center">How can we help with your video project today?</h2>
              
              <form onSubmit={handleSubmitPrompt} className="space-y-6">
                <div className="relative">
                  <textarea 
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Create a promotional video for a sustainable fashion brand that showcases their eco-friendly manufacturing process"
                    className="w-full min-h-[120px] p-4 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/30 resize-none text-sm"
                  />
                  <button
                    type="submit"
                    disabled={isSubmitting || !prompt.trim()}
                    className={`absolute bottom-3 right-3 p-2 rounded-md ${
                      isSubmitting || !prompt.trim()
                        ? "bg-muted text-muted-foreground cursor-not-allowed"
                        : "bg-primary text-primary-foreground hover:bg-primary/90"
                    } transition-colors`}
                    aria-label="Send prompt"
                  >
                    {isSubmitting ? (
                      <div className="animate-spin h-5 w-5 border-2 border-primary-foreground border-t-transparent rounded-full" />
                    ) : (
                      <Send className="h-5 w-5" />
                    )}
                  </button>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <button
                      type="button"
                      className="p-2 rounded-full bg-primary/10 hover:bg-primary/20 text-primary-foreground transition-colors"
                      aria-label="Add attachment"
                    >
                      <Plus className="h-4 w-4" />
                    </button>
                    <button
                      type="button" 
                      className="p-2 rounded-full bg-primary/10 hover:bg-primary/20 text-primary-foreground transition-colors"
                      aria-label="Generate with AI"
                    >
                      <Wand2 className="h-4 w-4" />
                    </button>
                    <span className="text-xs text-muted-foreground ml-2">
                      Generate with AI
                    </span>
                  </div>
                  
                  <button
                    type="submit"
                    disabled={isSubmitting || !prompt.trim()}
                    className={`px-4 py-2 rounded-md flex items-center gap-2 text-sm font-medium ${
                      isSubmitting || !prompt.trim()
                        ? "bg-muted text-muted-foreground cursor-not-allowed"
                        : "bg-primary text-primary-foreground hover:bg-primary/90"
                    } transition-colors`}
                  >
                    {isSubmitting ? "Processing..." : "Create Video Project"}
                    {!isSubmitting && <ArrowRight className="h-4 w-4" />}
                  </button>
                </div>
              </form>
              
              {searchResult && (
                <div className="mt-4 p-4 bg-blue-50/50 rounded-lg text-sm text-muted-foreground border border-blue-100">
                  <p className="font-medium text-blue-600 mb-1">AI Research Results:</p>
                  <p>{searchResult}</p>
                </div>
              )}
              
              <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="bg-secondary/10 hover:bg-secondary/20 cursor-pointer transition-colors">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-1">
                      <Film className="h-4 w-4 text-blue-500" />
                      <h3 className="font-medium">Brand Video Campaign</h3>
                    </div>
                    <p className="text-xs text-muted-foreground">Professional promotional videos for your brand or product</p>
                  </CardContent>
                </Card>
                <Card className="bg-secondary/10 hover:bg-secondary/20 cursor-pointer transition-colors">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-1">
                      <Play className="h-4 w-4 text-green-500" />
                      <h3 className="font-medium">Social Media Reels</h3>
                    </div>
                    <p className="text-xs text-muted-foreground">Short-form vertical videos optimized for social platforms</p>
                  </CardContent>
                </Card>
                <Card className="bg-secondary/10 hover:bg-secondary/20 cursor-pointer transition-colors">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-1">
                      <Camera className="h-4 w-4 text-amber-500" />
                      <h3 className="font-medium">Product Showcase</h3>
                    </div>
                    <p className="text-xs text-muted-foreground">Highlight features and benefits of your products with cinematic visuals</p>
                  </CardContent>
                </Card>
                <Card className="bg-secondary/10 hover:bg-secondary/20 cursor-pointer transition-colors">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-1">
                      <Video className="h-4 w-4 text-purple-500" />
                      <h3 className="font-medium">Explainer Video</h3>
                    </div>
                    <p className="text-xs text-muted-foreground">Clear, engaging explanations of your service or concept</p>
                  </CardContent>
                </Card>
              </div>
              
              <div className="mt-6 text-xs text-center text-muted-foreground">
                <p>Powered by Qwen AI with real-time internet search capabilities</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Right-side History Panel */}
      {showHistory && (
        <div className="fixed inset-0 z-50 flex">
          {/* Backdrop */}
          <div 
            className="flex-1 bg-black/20 backdrop-blur-sm"
            onClick={() => setShowHistory(false)}
          />
          
          {/* History Panel */}
          <div className="w-96 bg-white shadow-2xl border-l border-gray-200 flex flex-col animate-in slide-in-from-right duration-300">
            {/* Panel Header */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                  <History className="h-5 w-5" />
                  Recent Sessions
                </h3>
                <button
                  onClick={() => setShowHistory(false)}
                  className="p-1 hover:bg-gray-200 rounded-md transition-colors"
                  title="Close history panel"
                >
                  <XCircle className="h-5 w-5 text-gray-500" />
                </button>
            </div>
            </div>
            
            {/* Panel Content */}
            <div className="flex-1 overflow-y-auto p-4">
              {isLoadingHistory ? (
                <div className="text-center py-8">
                  <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full mx-auto mb-3" />
                  <p className="text-sm text-gray-500">Loading history...</p>
                </div>
              ) : sessionHistory.length > 0 ? (
                <div className="space-y-3">
                  {sessionHistory.map((session) => (
                    <div
                      key={session.id}
                      onClick={() => {
                        loadHistorySession(session)
                        setShowHistory(false)
                      }}
                      className="p-4 rounded-lg border border-gray-200 hover:border-primary/30 hover:bg-primary/5 cursor-pointer transition-all duration-200 group"
                    >
                      <p className="text-sm font-medium text-gray-900 line-clamp-2 group-hover:text-primary">
                        {session.prompt}
                      </p>
                      <p className="text-xs text-gray-500 mt-2">
                        {new Date(session.created_at).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                  ))}
                  </div>
              ) : (
                <div className="text-center py-8">
                  <History className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                  <p className="text-sm text-gray-500">No previous sessions found</p>
                  <p className="text-xs text-gray-400 mt-1">Your conversation history will appear here</p>
                </div>
              )}
            </div>
                </div>
              </div>
            )}
            
      {/* Invitation Code Overlay - Don't cover sidebar */}
      {!isCodeVerified && (
        <div className="fixed inset-0 z-[60] flex">
          {/* Left space for sidebar - don't cover it */}
          <div className="w-64 hidden lg:block" />
          
          {/* Main overlay area */}
          <div className="flex-1 backdrop-blur-md bg-background/70 flex items-center justify-center p-4">
            <div className="relative w-full max-w-4xl mx-auto max-h-[90vh] overflow-y-auto">
              <div className="bg-gradient-to-b from-background/95 to-background/90 border border-primary/20 shadow-2xl backdrop-blur-lg rounded-2xl overflow-hidden">
                {/* Decorative background elements */}
                <div className="absolute inset-0 overflow-hidden pointer-events-none">
                  <div className="absolute -top-20 -left-20 w-40 h-40 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-3xl"></div>
                  <div className="absolute -bottom-20 -right-20 w-40 h-40 bg-gradient-to-br from-indigo-500/20 to-cyan-500/20 rounded-full blur-3xl"></div>
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full blur-3xl"></div>
              </div>
              
              {/* Success animation overlay */}
              {showSuccessAnimation && (
                  <div className="absolute inset-0 z-20 bg-gradient-to-b from-background/90 to-background/90 backdrop-blur-sm flex items-center justify-center">
                  <div className="text-center">
                      <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-green-100 flex items-center justify-center">
                        <CheckCircle2 className="h-12 w-12 text-green-500 animate-pulse" />
                    </div>
                      <h3 className="text-2xl font-bold text-green-500 mb-2">Access Granted!</h3>
                      <p className="text-muted-foreground">Welcome to Nolan Studio</p>
                  </div>
              </div>
              )}
              
                {/* Header section */}
                <div className="relative z-10 text-center px-6 py-8 border-b border-primary/10">
                <div className="inline-flex items-center justify-center mb-6">
                    <div className="p-3 rounded-full bg-primary/10 mr-3">
                      <Lock className="h-8 w-8 text-primary" />
                    </div>
                    <div className="p-3 rounded-full bg-amber-500/10">
                  <Key className="h-8 w-8 text-amber-500" />
                    </div>
                  </div>
                  
                  <h1 className="text-2xl lg:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary via-purple-500 to-pink-500">
                    The World's First AI Agent Director: Nolan
                  </h1>
                  
                  <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary-foreground text-sm font-medium mb-4">
                    <Sparkles className="h-4 w-4 mr-2" />
                    <span>Early Access Program</span>
                </div>
              
                  <p className="text-base text-muted-foreground max-w-2xl mx-auto">
                    Experience the future of AI-powered film direction. Enter your invitation code or purchase access to unlock Nolan's revolutionary capabilities.
                  </p>
                </div>
                
                {/* Main content */}
                <div className="relative z-10 p-6 lg:p-8">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-4xl mx-auto">
                    {/* Left side - Invitation Code */}
                    <div className="space-y-6">
                      <div className="text-center lg:text-left">
                        <h3 className="text-xl font-semibold mb-2">Access with Invitation Code</h3>
                        <p className="text-muted-foreground">Already have an invitation code? Enter it below to get instant access.</p>
                      </div>
              
                      <form onSubmit={verifyInvitationCode} className="space-y-4">
                        <div className="relative">
                  <input
                    type="text"
                    value={invitationCode}
                    onChange={(e) => {
                      setInvitationCode(e.target.value)
                      setIsCodeError(false)
                      setErrorMessage("")
                    }}
                    placeholder="Enter invitation code"
                            className={`w-full px-4 py-4 rounded-xl bg-background/50 border-2 ${
                      isCodeError ? 'border-red-500' : 'border-primary/20'
                            } focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/40 placeholder-muted-foreground/50 text-center uppercase tracking-wider font-mono transition-all duration-200`}
                    maxLength={20}
                  />
                  {isCodeError && (
                            <div className="flex items-center justify-center gap-2 mt-2 text-red-500 text-sm">
                      <XCircle className="h-4 w-4" />
                      <span>{errorMessage}</span>
                    </div>
                  )}
                </div>
                
                <button
                  type="submit"
                  disabled={isVerifying || !invitationCode.trim()}
                          className={`w-full rounded-xl py-4 font-semibold transition-all duration-200 ${
                    isVerifying || !invitationCode.trim()
                      ? 'bg-muted text-muted-foreground cursor-not-allowed'
                            : 'bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg hover:shadow-xl'
                  }`}
                >
                  {isVerifying ? (
                    <div className="flex items-center justify-center gap-2">
                      <Clock className="h-4 w-4 animate-spin" />
                      <span>Verifying...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2">
                              <Key className="h-4 w-4" />
                              <span>Verify Code</span>
                    </div>
                  )}
                </button>
              </form>
                    </div>
              
                    {/* Right side - Purchase Options */}
                    <div className="space-y-6">
                      <div className="text-center lg:text-left">
                        <h3 className="text-xl font-semibold mb-2">Purchase Nolan Access</h3>
                        <p className="text-muted-foreground">Get instant access to Nolan's AI director capabilities.</p>
                      </div>
                      
                      <div className="space-y-4">
                        {/* Standard Nolan */}
                        <div className="relative group">
                          <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-300"></div>
                          <div className="relative bg-background/80 backdrop-blur-sm border border-primary/20 rounded-2xl p-4 hover:border-primary/40 transition-all duration-200">
                            <div className="flex items-center justify-between mb-3">
                              <div>
                                <h4 className="text-lg font-semibold">Standard Nolan</h4>
                                <p className="text-sm text-muted-foreground">Basic AI director features</p>
                              </div>
                              <div className="text-right">
                                <div className="text-2xl font-bold text-primary">$399</div>
                                <div className="text-xs text-muted-foreground">Monthly subscription</div>
                              </div>
                            </div>
                            
                            <ul className="space-y-1 mb-4 text-sm">
                              <li className="flex items-center gap-2">
                                <CheckCircle2 className="h-3 w-3 text-green-500 flex-shrink-0" />
                                <span>AI-powered script analysis</span>
                              </li>
                              <li className="flex items-center gap-2">
                                <CheckCircle2 className="h-3 w-3 text-green-500 flex-shrink-0" />
                                <span>Automated shot planning</span>
                              </li>
                              <li className="flex items-center gap-2">
                                <CheckCircle2 className="h-3 w-3 text-green-500 flex-shrink-0" />
                                <span>Basic video generation</span>
                              </li>
                            </ul>
                            
                            <button className="w-full bg-primary text-primary-foreground hover:bg-primary/90 py-2.5 rounded-xl font-semibold transition-colors">
                              Purchase Standard
                            </button>
                          </div>
                        </div>

                        {/* Premium Nolan */}
                        <div className="relative group">
                          <div className="absolute -inset-1 bg-gradient-to-r from-amber-500 to-orange-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-300"></div>
                          <div className="relative bg-background/80 backdrop-blur-sm border border-amber-500/30 rounded-2xl p-4 hover:border-amber-500/50 transition-all duration-200">
                            <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                              <span className="bg-gradient-to-r from-amber-500 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                                PREMIUM
                              </span>
                            </div>
                            
                            <div className="flex items-center justify-between mb-3 mt-1">
                              <div>
                                <h4 className="text-lg font-semibold">Premium Nolan</h4>
                                <p className="text-sm text-muted-foreground">Advanced AI director with premium features</p>
                    </div>
                              <div className="text-right">
                                <div className="text-2xl font-bold text-amber-500">$999</div>
                                <div className="text-xs text-muted-foreground">Monthly subscription</div>
                  </div>
                  </div>
                            
                            <ul className="space-y-1 mb-4 text-sm">
                              <li className="flex items-center gap-2">
                                <CheckCircle2 className="h-3 w-3 text-green-500 flex-shrink-0" />
                                <span>Everything in Standard</span>
                              </li>
                              <li className="flex items-center gap-2">
                                <CheckCircle2 className="h-3 w-3 text-green-500 flex-shrink-0" />
                                <span>Advanced cinematography AI</span>
                              </li>
                              <li className="flex items-center gap-2">
                                <CheckCircle2 className="h-3 w-3 text-green-500 flex-shrink-0" />
                                <span>Multi-camera coordination</span>
                              </li>
                              <li className="flex items-center gap-2">
                                <CheckCircle2 className="h-3 w-3 text-green-500 flex-shrink-0" />
                                <span>Priority processing</span>
                              </li>
                              <li className="flex items-center gap-2">
                                <CheckCircle2 className="h-3 w-3 text-green-500 flex-shrink-0" />
                                <span>24/7 premium support</span>
                              </li>
                            </ul>
                            
                            <button className="w-full bg-gradient-to-r from-amber-500 to-orange-500 text-white hover:from-amber-600 hover:to-orange-600 py-2.5 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl">
                              Purchase Premium
                            </button>
                  </div>
                  </div>
                  </div>
                  </div>
                  </div>
                  
                  {/* Footer */}
                  <div className="mt-8 text-center text-sm text-muted-foreground">
                    <p>Secure payment processing • 30-day money-back guarantee • Instant access</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 