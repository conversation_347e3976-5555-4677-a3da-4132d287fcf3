"use client";

import { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>lider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useSoundStore, VoiceModel } from "@/store/useSoundStore";
import { useVoiceModels } from "../hooks/useVoiceModels";
import { Volume2, Play, Pause, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";



export function SettingsPanel() {
    const {
        selectedVoiceModel,
        speed,
        stability,
        similarityBoost,
        styleExaggeration,
        setSpeed,
        setStability,
        setSimilarityBoost,
        setStyleExaggeration,
        resetSettings,
    } = useSoundStore();

    const { availableVoiceModels, selectVoiceModel } = useVoiceModels();
    const [isPlayingPreview, setIsPlayingPreview] = useState(false);

    const handlePlayPreview = useCallback(() => {
        if (!selectedVoiceModel) return;

        setIsPlayingPreview(true);
        // TODO: Implement voice preview playback
        setTimeout(() => {
            setIsPlayingPreview(false);
        }, 2000);
    }, [selectedVoiceModel]);

    return (
        <div className="w-80 bg-gray-900/30 border-l border-gray-800 p-6 overflow-y-auto">
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-white">Settings</h2>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={resetSettings}
                        className="text-gray-400 hover:text-white text-xs"
                    >
                        Reset
                    </Button>
                </div>

                {/* Voice Selection */}
                <Card className="bg-gray-800/50 border-gray-700">
                    <CardHeader className="pb-3">
                        <CardTitle className="text-white text-sm font-medium flex items-center">
                            <Volume2 className="w-4 h-4 mr-2" />
                            Voice
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                        <Select
                            value={selectedVoiceModel?.id || ""}
                            onValueChange={selectVoiceModel}
                        >
                            <SelectTrigger className="bg-gray-900/50 border-gray-600 text-white">
                                <SelectValue placeholder="Select a voice model" />
                            </SelectTrigger>
                            <SelectContent className="bg-gray-800 border-gray-600">
                                {availableVoiceModels.map((model) => (
                                    <SelectItem
                                        key={model.id}
                                        value={model.id}
                                        className="text-white hover:bg-gray-700"
                                    >
                                        <div className="flex flex-col">
                                            <span className="font-medium">{model.name}</span>
                                            <span className="text-xs text-gray-400">{model.description}</span>
                                        </div>
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>

                        {selectedVoiceModel && (
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={handlePlayPreview}
                                disabled={isPlayingPreview}
                                className="w-full bg-gray-900/50 border-gray-600 text-white hover:bg-gray-700"
                            >
                                {isPlayingPreview ? (
                                    <>
                                        <Pause className="w-3 h-3 mr-2" />
                                        Playing...
                                    </>
                                ) : (
                                    <>
                                        <Play className="w-3 h-3 mr-2" />
                                        Preview Voice
                                    </>
                                )}
                            </Button>
                        )}
                    </CardContent>
                </Card>

                {/* Model Selection */}
                <Card className="bg-gray-800/50 border-gray-700">
                    <CardHeader className="pb-3">
                        <CardTitle className="text-white text-sm font-medium">Model</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Select defaultValue="eleven-multilingual-v2">
                            <SelectTrigger className="bg-gray-900/50 border-gray-600 text-white">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent className="bg-gray-800 border-gray-600">
                                <SelectItem value="eleven-multilingual-v2" className="text-white hover:bg-gray-700">
                                    Eleven Multilingual v2
                                </SelectItem>
                                <SelectItem value="eleven-turbo-v2" className="text-white hover:bg-gray-700">
                                    Eleven Turbo v2
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </CardContent>
                </Card>

                {/* Speed Control */}
                <Card className="bg-gray-800/50 border-gray-700">
                    <CardHeader className="pb-3">
                        <CardTitle className="text-white text-sm font-medium flex items-center justify-between">
                            Speed
                            <span className="text-xs text-gray-400">{speed.toFixed(2)}</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Slider
                            value={[speed]}
                            onValueChange={(value) => setSpeed(value[0])}
                            min={0.25}
                            max={4.0}
                            step={0.05}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                            <span>0.25x</span>
                            <span>4.0x</span>
                        </div>
                    </CardContent>
                </Card>

                {/* Stability Control */}
                <Card className="bg-gray-800/50 border-gray-700">
                    <CardHeader className="pb-3">
                        <CardTitle className="text-white text-sm font-medium flex items-center justify-between">
                            Stability
                            <span className="text-xs text-gray-400">{(stability * 100).toFixed(0)}%</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Slider
                            value={[stability]}
                            onValueChange={(value) => setStability(value[0])}
                            min={0}
                            max={1}
                            step={0.01}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                            <span>Variable</span>
                            <span>Stable</span>
                        </div>
                    </CardContent>
                </Card>

                {/* Similarity Boost Control */}
                <Card className="bg-gray-800/50 border-gray-700">
                    <CardHeader className="pb-3">
                        <CardTitle className="text-white text-sm font-medium flex items-center justify-between">
                            Similarity Boost
                            <span className="text-xs text-gray-400">{(similarityBoost * 100).toFixed(0)}%</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Slider
                            value={[similarityBoost]}
                            onValueChange={(value) => setSimilarityBoost(value[0])}
                            min={0}
                            max={1}
                            step={0.01}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                            <span>Low</span>
                            <span>High</span>
                        </div>
                    </CardContent>
                </Card>

                {/* Style Exaggeration Control */}
                <Card className="bg-gray-800/50 border-gray-700">
                    <CardHeader className="pb-3">
                        <CardTitle className="text-white text-sm font-medium flex items-center justify-between">
                            Style Exaggeration
                            <span className="text-xs text-gray-400">{(styleExaggeration * 100).toFixed(0)}%</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Slider
                            value={[styleExaggeration]}
                            onValueChange={(value) => setStyleExaggeration(value[0])}
                            min={0}
                            max={1}
                            step={0.01}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                            <span>None</span>
                            <span>Exaggerated</span>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
