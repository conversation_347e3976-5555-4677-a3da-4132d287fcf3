import { ReactNode } from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
    title: "Sound Studio - AI Voice Generation | ReelMind",
    description: "Generate high-quality AI voices with advanced text-to-speech technology. Choose from various voice models and customize speed, stability, and style.",
    keywords: ["AI voice", "text to speech", "voice generation", "TTS", "sound studio"],
};

export default function SoundLayout({
    children,
}: {
    children: ReactNode;
}) {
    return (
        <div className="h-full">
            {children}
        </div>
    );
}
