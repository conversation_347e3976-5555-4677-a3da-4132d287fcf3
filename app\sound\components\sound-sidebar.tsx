"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useSoundStore } from "@/store/useSoundStore";
import { 
    ChevronLeft, 
    ChevronRight, 
    Volume2, 
    Settings, 
    History, 
    HelpCircle,
    FileText,
    Mic
} from "lucide-react";
import { cn } from "@/lib/utils";
import Link from "next/link";

interface SidebarItemProps {
    icon: React.ComponentType<{ className?: string }>;
    label: string;
    isActive?: boolean;
    onClick?: () => void;
    href?: string;
    collapsed?: boolean;
}

function SidebarItem({ icon: Icon, label, isActive, onClick, href, collapsed }: SidebarItemProps) {
    const content = (
        <div
            className={cn(
                "flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors cursor-pointer",
                isActive 
                    ? "bg-white/10 text-white" 
                    : "text-gray-400 hover:text-white hover:bg-white/5",
                collapsed && "justify-center"
            )}
            onClick={onClick}
        >
            <Icon className="w-5 h-5 flex-shrink-0" />
            {!collapsed && <span className="text-sm font-medium">{label}</span>}
        </div>
    );

    if (href) {
        return (
            <Link href={href}>
                {content}
            </Link>
        );
    }

    return content;
}

export function SoundSidebar() {
    const { isSidebarCollapsed, setIsSidebarCollapsed } = useSoundStore();
    const [activeSection, setActiveSection] = useState<string>("voice-generation");

    const toggleSidebar = () => {
        setIsSidebarCollapsed(!isSidebarCollapsed);
    };

    const sidebarItems = [
        {
            id: "voice-generation",
            icon: Volume2,
            label: "Voice Generation",
            isActive: activeSection === "voice-generation",
            onClick: () => setActiveSection("voice-generation"),
        },
        {
            id: "voice-cloning",
            icon: Mic,
            label: "Voice Cloning",
            onClick: () => setActiveSection("voice-cloning"),
        },
        {
            id: "projects",
            icon: FileText,
            label: "Projects",
            onClick: () => setActiveSection("projects"),
        },
        {
            id: "history",
            icon: History,
            label: "History",
            onClick: () => setActiveSection("history"),
        },
    ];

    const bottomItems = [
        {
            id: "settings",
            icon: Settings,
            label: "Settings",
            onClick: () => setActiveSection("settings"),
        },
        {
            id: "help",
            icon: HelpCircle,
            label: "Help & Support",
            onClick: () => setActiveSection("help"),
        },
    ];

    return (
        <div
            className={cn(
                "bg-gray-900/50 border-r border-gray-800 flex flex-col transition-all duration-300",
                isSidebarCollapsed ? "w-16" : "w-64"
            )}
        >
            {/* Header */}
            <div className="p-4 border-b border-gray-800">
                <div className="flex items-center justify-between">
                    {!isSidebarCollapsed && (
                        <div className="flex items-center space-x-2">
                            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                                <Volume2 className="w-4 h-4 text-white" />
                            </div>
                            <div>
                                <h1 className="text-white font-semibold text-sm">Sound Studio</h1>
                                <p className="text-gray-400 text-xs">AI Voice Generation</p>
                            </div>
                        </div>
                    )}
                    
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={toggleSidebar}
                        className="text-gray-400 hover:text-white p-1 h-8 w-8"
                    >
                        {isSidebarCollapsed ? (
                            <ChevronRight className="w-4 h-4" />
                        ) : (
                            <ChevronLeft className="w-4 h-4" />
                        )}
                    </Button>
                </div>
            </div>

            {/* Navigation Items */}
            <div className="flex-1 p-4 space-y-2">
                {sidebarItems.map((item) => (
                    <SidebarItem
                        key={item.id}
                        icon={item.icon}
                        label={item.label}
                        isActive={item.isActive}
                        onClick={item.onClick}
                        collapsed={isSidebarCollapsed}
                    />
                ))}
            </div>

            {/* Bottom Items */}
            <div className="p-4 border-t border-gray-800 space-y-2">
                {bottomItems.map((item) => (
                    <SidebarItem
                        key={item.id}
                        icon={item.icon}
                        label={item.label}
                        onClick={item.onClick}
                        collapsed={isSidebarCollapsed}
                    />
                ))}
            </div>

            {/* User Info (if not collapsed) */}
            {!isSidebarCollapsed && (
                <div className="p-4 border-t border-gray-800">
                    <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center">
                            <span className="text-white text-sm font-medium">U</span>
                        </div>
                        <div className="flex-1 min-w-0">
                            <p className="text-white text-sm font-medium truncate">User</p>
                            <p className="text-gray-400 text-xs truncate">Free Plan</p>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
