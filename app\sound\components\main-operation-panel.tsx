"use client";

import { useState, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useSoundStore } from "@/store/useSoundStore";
import { useSoundGeneration } from "../hooks/useSoundGeneration";
import { Loader2, Play, Volume2 } from "lucide-react";
import { cn } from "@/lib/utils";

export function MainOperationPanel() {
    const {
        text,
        characterCount,
        maxCharacters,
        selectedVoiceModel,
        error,
        setText,
    } = useSoundStore();

    const { generateSpeech, canGenerate, isGenerating } = useSoundGeneration();
    const [isPlaying, setIsPlaying] = useState(false);

    const handleTextChange = useCallback((value: string) => {
        setText(value);
    }, [setText]);

    return (
        <div className="flex flex-col h-full p-6">
            {/* Header */}
            <div className="mb-6">
                <h1 className="text-2xl font-semibold text-white mb-2">Text to Speech</h1>
                <p className="text-gray-400 text-sm">
                    Enter your text below and generate high-quality AI voice
                </p>
            </div>

            {/* Main Text Input Area */}
            <div className="flex-1 flex flex-col">
                <div className="flex-1 mb-4">
                    <Textarea
                        value={text}
                        onChange={(e) => handleTextChange(e.target.value)}
                        placeholder="Enter typing here or paste any text you want to turn into lifelike speech..."
                        className={cn(
                            "w-full h-full resize-none text-base leading-relaxed",
                            "bg-gray-900/50 border-gray-700 text-white placeholder:text-gray-500",
                            "focus:border-gray-600 focus:ring-1 focus:ring-gray-600",
                            "rounded-lg p-4",
                            error && characterCount > maxCharacters && "border-red-500 focus:border-red-500 focus:ring-red-500"
                        )}
                        style={{ minHeight: "300px" }}
                    />
                </div>

                {/* Bottom Controls */}
                <div className="flex items-center justify-between">
                    {/* Character Count */}
                    <div className="flex items-center space-x-4">
                        <div className={cn(
                            "text-sm",
                            characterCount > maxCharacters ? "text-red-400" : "text-gray-400"
                        )}>
                            {characterCount.toLocaleString()} / {maxCharacters.toLocaleString()} characters
                        </div>

                        {/* Voice Model Indicator */}
                        {selectedVoiceModel && (
                            <div className="flex items-center space-x-2 text-sm text-gray-400">
                                <Volume2 className="w-4 h-4" />
                                <span>{selectedVoiceModel.name}</span>
                            </div>
                        )}
                    </div>

                    {/* Generate Button */}
                    <Button
                        onClick={generateSpeech}
                        disabled={!canGenerate}
                        className={cn(
                            "px-8 py-2 font-medium",
                            "bg-white text-black hover:bg-gray-100",
                            "disabled:opacity-50 disabled:cursor-not-allowed",
                            "transition-all duration-200"
                        )}
                    >
                        {isGenerating ? (
                            <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Generating...
                            </>
                        ) : (
                            <>
                                <Play className="w-4 h-4 mr-2" />
                                Generate Speech
                            </>
                        )}
                    </Button>
                </div>

                {/* Error Message */}
                {error && (
                    <div className="mt-3 p-3 bg-red-900/20 border border-red-800 rounded-lg">
                        <p className="text-red-400 text-sm">{error}</p>
                    </div>
                )}
            </div>
        </div>
    );
}
