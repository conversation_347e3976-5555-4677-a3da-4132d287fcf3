import { useCallback, useEffect } from 'react';
import { useSoundStore, VoiceModel } from '@/store/useSoundStore';
import { soundApi, handleApiError } from '@/lib/api/sound';

// Mock voice models for development
const mockVoiceModels: VoiceModel[] = [
    {
        id: "brian-multilingual",
        name: "<PERSON> (Multilingual)",
        description: "Deep, authoritative male voice with multilingual support",
        language: "en",
        gender: "male",
        preview_url: "/audio/brian-preview.mp3",
    },
    {
        id: "sarah-english",
        name: "<PERSON> (English)",
        description: "Clear, professional female voice perfect for business content",
        language: "en",
        gender: "female",
        preview_url: "/audio/sarah-preview.mp3",
    },
    {
        id: "james-narrative",
        name: "<PERSON> (Narrative)",
        description: "Warm, storytelling male voice ideal for audiobooks and narration",
        language: "en",
        gender: "male",
        preview_url: "/audio/james-preview.mp3",
    },
    {
        id: "emma-conversational",
        name: "<PERSON> (Conversational)",
        description: "Friendly, natural female voice great for casual content",
        language: "en",
        gender: "female",
        preview_url: "/audio/emma-preview.mp3",
    },
    {
        id: "alex-news",
        name: "<PERSON> (News)",
        description: "Professional news anchor style voice",
        language: "en",
        gender: "male",
        preview_url: "/audio/alex-preview.mp3",
    },
    {
        id: "sophia-elegant",
        name: "Sophia (Elegant)",
        description: "Sophisticated and elegant female voice",
        language: "en",
        gender: "female",
        preview_url: "/audio/sophia-preview.mp3",
    },
];

export function useVoiceModels() {
    const {
        availableVoiceModels,
        selectedVoiceModel,
        setAvailableVoiceModels,
        setSelectedVoiceModel,
    } = useSoundStore();

    const loadVoiceModels = useCallback(async () => {
        try {
            // Try to load from API first
            const response = await soundApi.getVoiceModels();
            
            if (response.success && response.data?.models) {
                setAvailableVoiceModels(response.data.models);
                
                // Auto-select first model if none selected
                if (!selectedVoiceModel && response.data.models.length > 0) {
                    setSelectedVoiceModel(response.data.models[0]);
                }
            } else {
                // Fallback to mock data
                console.warn('Failed to load voice models from API, using mock data');
                setAvailableVoiceModels(mockVoiceModels);
                
                if (!selectedVoiceModel) {
                    setSelectedVoiceModel(mockVoiceModels[0]);
                }
            }
        } catch (error) {
            console.error('Failed to load voice models:', error);
            // Use mock data as fallback
            setAvailableVoiceModels(mockVoiceModels);
            
            if (!selectedVoiceModel) {
                setSelectedVoiceModel(mockVoiceModels[0]);
            }
        }
    }, [availableVoiceModels, selectedVoiceModel, setAvailableVoiceModels, setSelectedVoiceModel]);

    // Load voice models on mount
    useEffect(() => {
        if (availableVoiceModels.length === 0) {
            loadVoiceModels();
        }
    }, [availableVoiceModels.length, loadVoiceModels]);

    const selectVoiceModel = useCallback((modelId: string) => {
        const model = availableVoiceModels.find(m => m.id === modelId);
        if (model) {
            setSelectedVoiceModel(model);
        }
    }, [availableVoiceModels, setSelectedVoiceModel]);

    const getVoiceModelsByGender = useCallback((gender: 'male' | 'female' | 'neutral') => {
        return availableVoiceModels.filter(model => model.gender === gender);
    }, [availableVoiceModels]);

    const getVoiceModelsByLanguage = useCallback((language: string) => {
        return availableVoiceModels.filter(model => model.language === language);
    }, [availableVoiceModels]);

    return {
        availableVoiceModels,
        selectedVoiceModel,
        selectVoiceModel,
        loadVoiceModels,
        getVoiceModelsByGender,
        getVoiceModelsByLanguage,
    };
}
