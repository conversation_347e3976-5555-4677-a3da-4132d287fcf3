"use client";

import { useState, useEffect } from "react";
import { MainOperationPanel } from "./main-operation-panel";
import { SettingsPanel } from "./settings-panel";
import { HistoryPanel } from "./history-panel";
import { SoundSidebar } from "./sound-sidebar";
import { MobileSoundLayout } from "./mobile-sound-layout";

export function SoundPageContent() {
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth < 768);
        };

        checkMobile();
        window.addEventListener('resize', checkMobile);
        return () => window.removeEventListener('resize', checkMobile);
    }, []);

    if (isMobile) {
        return <MobileSoundLayout />;
    }

    return (
        <div className="relative z-10 flex h-full overflow-hidden dark:bg-radial from-gray-900 via-gray-950 to-black">
            {/* Left Sidebar - Hidden on smaller screens */}
            <div className="hidden lg:block">
                <SoundSidebar />
            </div>

            {/* Main Content Area */}
            <div className="flex-1 flex overflow-hidden">
                {/* Main Operation Panel */}
                <div className="flex-1 flex flex-col min-w-0">
                    <MainOperationPanel />
                </div>

                {/* Right Settings Panel - Hidden on medium screens */}
                <div className="hidden xl:block">
                    <SettingsPanel />
                </div>
            </div>

            {/* History Panel - Hidden on smaller screens */}
            <div className="hidden lg:block">
                <HistoryPanel />
            </div>
        </div>
    );
}
